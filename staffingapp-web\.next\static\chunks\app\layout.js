/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSG9tZSU1QyU1Q0RvY3VtZW50cyU1QyU1Q1Byb2plY3RzJTVDJTVDc3RhZmZpbmdhcHAlNUMlNUNzdGFmZmluZ2FwcC13ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyc3JjJTVDJTVDJTVDJTVDYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSG9tZSU1QyU1Q0RvY3VtZW50cyU1QyU1Q1Byb2plY3RzJTVDJTVDc3RhZmZpbmdhcHAlNUMlNUNzdGFmZmluZ2FwcC13ZWIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGtjQUF1UjtBQUN2UjtBQUNBLG9LQUE4SCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzhjMjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIb21lXFxcXERvY3VtZW50c1xcXFxQcm9qZWN0c1xcXFxzdGFmZmluZ2FwcFxcXFxzdGFmZmluZ2FwcC13ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZm9udFxcXFxnb29nbGVcXFxcdGFyZ2V0LmNzcz97XFxcInBhdGhcXFwiOlxcXCJzcmNcXFxcXFxcXGFwcFxcXFxcXFxcbGF5b3V0LnRzeFxcXCIsXFxcImltcG9ydFxcXCI6XFxcIkludGVyXFxcIixcXFwiYXJndW1lbnRzXFxcIjpbe1xcXCJzdWJzZXRzXFxcIjpbXFxcImxhdGluXFxcIl19XSxcXFwidmFyaWFibGVOYW1lXFxcIjpcXFwiaW50ZXJcXFwifVwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSG9tZVxcXFxEb2N1bWVudHNcXFxcUHJvamVjdHNcXFxcc3RhZmZpbmdhcHBcXFxcc3RhZmZpbmdhcHAtd2ViXFxcXHNyY1xcXFxhcHBcXFxcZ2xvYmFscy5jc3NcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={592:(e,r,t)=>{var n=t(722);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},722:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(592);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"3a07ccceb1b6\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MzBhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjNhMDdjY2NlYjFiNlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\"};\n    if(true) {\n      // 1750989025084\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwic3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIsXCJpbXBvcnRcIjpcIkludGVyXCIsXCJhcmd1bWVudHNcIjpbe1wic3Vic2V0c1wiOltcImxhdGluXCJdfV0sXCJ2YXJpYWJsZU5hbWVcIjpcImludGVyXCJ9IiwibWFwcGluZ3MiOiJBQUFBO0FBQ0Esa0JBQWtCLFNBQVMsZ0ZBQWdGO0FBQzNHLE9BQU8sSUFBVTtBQUNqQjtBQUNBLHNCQUFzQixtQkFBTyxDQUFDLHdNQUFrSixjQUFjLHNEQUFzRDtBQUNwUCxNQUFNLFVBQVU7QUFDaEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/OWY5YiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBleHRyYWN0ZWQgYnkgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cbm1vZHVsZS5leHBvcnRzID0ge1wic3R5bGVcIjp7XCJmb250RmFtaWx5XCI6XCInX19JbnRlcl9lOGNlMGMnLCAnX19JbnRlcl9GYWxsYmFja19lOGNlMGMnXCIsXCJmb250U3R5bGVcIjpcIm5vcm1hbFwifSxcImNsYXNzTmFtZVwiOlwiX19jbGFzc05hbWVfZThjZTBjXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTA5ODkwMjUwODRcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiQzovVXNlcnMvSG9tZS9Eb2N1bWVudHMvUHJvamVjdHMvc3RhZmZpbmdhcHAvc3RhZmZpbmdhcHAtd2ViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzXCIpKG1vZHVsZS5pZCwge1wicHVibGljUGF0aFwiOlwiL19uZXh0L1wiLFwiZXNNb2R1bGVcIjpmYWxzZSxcImxvY2Fsc1wiOnRydWV9KTtcbiAgICAgIG1vZHVsZS5ob3QuZGlzcG9zZShjc3NSZWxvYWQpO1xuICAgICAgXG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);