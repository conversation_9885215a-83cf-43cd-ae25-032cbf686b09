"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: function() { return /* binding */ getRoleBasedStatusDisplay; },\n/* harmony export */   getStatusColors: function() { return /* binding */ getStatusColors; },\n/* harmony export */   getStatusTailwindClasses: function() { return /* binding */ getStatusTailwindClasses; }\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Enhanced debug logging to see what we're receiving\n    console.log(\"\\uD83D\\uDD0D Status Display Debug:\", {\n        originalStatus: status,\n        userRole,\n        statusType: typeof status,\n        userRoleType: typeof userRole\n    });\n    // Check for null/undefined status\n    if (!status) {\n        console.warn(\"⚠️ Received null or undefined status\");\n        return \"Unknown\";\n    }\n    // Normalize status to handle both backend enum names and frontend display names\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\").toLowerCase();\n    console.log(\"\\uD83D\\uDCDD Normalized status:\", normalizedStatus, \"Original:\", status);\n    let result = status; // Default fallback\n    // Log which case we're entering\n    console.log(\"\\uD83D\\uDD04 Processing for role: \".concat(userRole));\n    switch(userRole){\n        case \"user\":\n            console.log(\"\\uD83D\\uDC64 User role case, normalized status: \".concat(normalizedStatus));\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"Submitted\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"released\":\n                    result = \"Completed\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    console.warn(\"⚠️ Unrecognized status for User role: \".concat(normalizedStatus, \" (original: \").concat(status, \")\"));\n                    result = status;\n            }\n            break;\n        case \"Supervisor\":\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting My Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Staff\":\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned to Me\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"Admin\":\n            // Admin sees the system status names\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        default:\n            // Default to system status names\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n    }\n    console.log(\"✅ Final result:\", {\n        originalStatus: status,\n        userRole,\n        normalizedStatus,\n        result\n    });\n    return result;\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/statusDisplay.ts\n"));

/***/ })

});