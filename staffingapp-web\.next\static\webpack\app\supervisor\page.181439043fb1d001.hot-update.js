"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/supervisor/page",{

/***/ "(app-pages-browser)/./src/app/supervisor/page.tsx":
/*!*************************************!*\
  !*** ./src/app/supervisor/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SupervisorDashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(app-pages-browser)/./src/utils/statusDisplay.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Utility function to format enum names with proper spacing\nconst formatEnumName = (enumValue)=>{\n    return enumValue.replace(/([A-Z])/g, \" $1\").trim();\n};\nfunction SupervisorDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [staffMembers, setStaffMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pending\");\n    const [showAssignModal, setShowAssignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedJob, setSelectedJob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assignmentData, setAssignmentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        staffId: \"\",\n        notes: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = ()=>{\n            const token = sessionStorage.getItem(\"token\");\n            const userStr = sessionStorage.getItem(\"user\");\n            if (!token || !userStr) {\n                router.push(\"/login\");\n                return;\n            }\n            const userData = JSON.parse(userStr);\n            if (userData.role !== \"Supervisor\") {\n                router.push(\"/dashboard\");\n                return;\n            }\n            setUser(userData);\n            fetchJobs();\n            fetchStaffMembers();\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const fetchJobs = async ()=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/jobs/all\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const jobsData = await response.json();\n                setJobs(jobsData);\n            } else {\n                console.error(\"Failed to fetch jobs:\", response.status, response.statusText);\n            }\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchStaffMembers = async ()=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/auth/users/staff\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const staffData = await response.json();\n                setStaffMembers(staffData);\n            } else {\n                console.error(\"Failed to fetch staff members:\", response.status, response.statusText);\n            }\n        } catch (error) {\n            console.error(\"Error fetching staff members:\", error);\n        }\n    };\n    const handleLogout = ()=>{\n        sessionStorage.clear();\n        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n        window.location.href = \"/login\";\n    };\n    const handleAssignJob = (job)=>{\n        setSelectedJob(job);\n        setShowAssignModal(true);\n        setAssignmentData({\n            staffId: \"\",\n            notes: \"\"\n        });\n    };\n    const handleRejectJob = async (jobId)=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/jobs/\".concat(jobId, \"/reject\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                fetchJobs(); // Refresh the jobs list\n            }\n        } catch (error) {\n            console.error(\"Error rejecting job:\", error);\n        }\n    };\n    const submitAssignment = async ()=>{\n        if (!selectedJob || !assignmentData.staffId) return;\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/jobs/\".concat(selectedJob.id, \"/assign\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(token),\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    staffId: parseInt(assignmentData.staffId),\n                    notes: assignmentData.notes\n                })\n            });\n            if (response.ok) {\n                setShowAssignModal(false);\n                setSelectedJob(null);\n                setAssignmentData({\n                    staffId: \"\",\n                    notes: \"\"\n                });\n                fetchJobs(); // Refresh the jobs list\n            }\n        } catch (error) {\n            console.error(\"Error assigning job:\", error);\n        }\n    };\n    const getJobsByStatus = (status)=>{\n        return jobs.filter((job)=>job.status === status);\n    };\n    const getStatusColor = (status)=>{\n        return (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getStatusTailwindClasses)(status);\n    };\n    const getStatusDisplay = (status)=>{\n        return (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(status, \"Supervisor\");\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading supervisor dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this);\n    }\n    const pendingJobs = getJobsByStatus(\"New\");\n    const underReviewJobs = getJobsByStatus(\"Returned\");\n    const assignedJobs = getJobsByStatus(\"Assigned\");\n    const inProgressJobs = getJobsByStatus(\"InProgress\");\n    const completedJobs = getJobsByStatus(\"AwaitingReview\"); // Jobs completed by staff, awaiting supervisor review\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Staff Hall - Supervisor Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: [\n                                            \"Welcome, \",\n                                            user === null || user === void 0 ? void 0 : user.firstName,\n                                            \" \",\n                                            user === null || user === void 0 ? void 0 : user.lastName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: pendingJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"New\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    pendingJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: assignedJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"Assigned\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    assignedJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: inProgressJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"InProgress\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    inProgressJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: completedJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"AwaitingReview\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    completedJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: staffMembers.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: \"Staff Members\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    staffMembers.length,\n                                                                    \" active\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"-mb-px flex space-x-8 px-6\",\n                                        \"aria-label\": \"Tabs\",\n                                        children: [\n                                            {\n                                                id: \"pending\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"New\", \"Supervisor\"),\n                                                count: pendingJobs.length\n                                            },\n                                            {\n                                                id: \"assigned\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"Assigned\", \"Supervisor\"),\n                                                count: assignedJobs.length\n                                            },\n                                            {\n                                                id: \"progress\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"InProgress\", \"Supervisor\"),\n                                                count: inProgressJobs.length\n                                            },\n                                            {\n                                                id: \"completed\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"AwaitingReview\", \"Supervisor\"),\n                                                count: completedJobs.length\n                                            },\n                                            {\n                                                id: \"staff\",\n                                                name: \"Staff Management\",\n                                                count: staffMembers.length\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"\".concat(activeTab === tab.id ? \"border-emerald-500 text-emerald-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm\"),\n                                                children: [\n                                                    tab.name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs\",\n                                                        children: tab.count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        activeTab === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: [\n                                                        (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"New\", \"Supervisor\"),\n                                                        \" Jobs\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                pendingJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No pending jobs to review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: pendingJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 366,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 367,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Submitted: \",\n                                                                                            formatDate(job.createdAt)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 368,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAssignJob(job),\n                                                                                className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"Assign\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 378,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleRejectJob(job.id),\n                                                                                className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"Reject\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"assigned\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Assigned Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this),\n                                                assignedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No assigned jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: assignedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-purple-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 413,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 414,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Assigned: \",\n                                                                                            job.assignedAt ? formatDate(job.assignedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 415,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Assigned to: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 420,\n                                                                                            columnNumber: 50\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 418,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            job.supervisorNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 p-2 bg-blue-50 rounded\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-blue-800\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: \"Notes:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 427,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        job.supervisorNotes\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 426,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex px-3 py-1 text-sm font-semibold rounded-full \".concat(getStatusColor(job.status)),\n                                                                                children: getStatusDisplay(job.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"progress\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Jobs In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                inProgressJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No jobs in progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: inProgressJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-emerald-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 465,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 466,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Started: \",\n                                                                                            job.assignedAt ? formatDate(job.assignedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 467,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Being worked on by: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 472,\n                                                                                            columnNumber: 57\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"inline-flex px-3 py-1 text-sm font-semibold rounded-full \".concat(getStatusColor(job.status)),\n                                                                                children: getStatusDisplay(job.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 484,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Jobs Awaiting Supervisor Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this),\n                                                completedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No completed jobs awaiting review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: completedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-green-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 510,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 511,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Completed: \",\n                                                                                            job.completedAt ? formatDate(job.completedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 512,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Completed by: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 517,\n                                                                                            columnNumber: 51\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 515,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            job.outputDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 p-3 bg-blue-50 rounded-md\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-blue-800\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: \"Output Details:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 524,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        job.outputDetails\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 523,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 522,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                                            className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded text-sm font-medium\",\n                                                                            children: \"Review & Deliver\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"staff\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Staff Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this),\n                                                staffMembers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No staff members found\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: staffMembers.map((staff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        staff.firstName,\n                                                                        \" \",\n                                                                        staff.lastName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 text-sm\",\n                                                                    children: staff.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800\",\n                                                                        children: formatEnumName(staff.role)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, staff.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            showAssignModal && selectedJob && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"Assign Job: \",\n                                    selectedJob.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Assign to Staff Member\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: assignmentData.staffId,\n                                        onChange: (e)=>setAssignmentData({\n                                                ...assignmentData,\n                                                staffId: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                        required: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select staff member...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, this),\n                                            staffMembers.map((staff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: staff.id,\n                                                    children: [\n                                                        staff.firstName,\n                                                        \" \",\n                                                        staff.lastName,\n                                                        \" (\",\n                                                        staff.email,\n                                                        \")\"\n                                                    ]\n                                                }, staff.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Notes for Staff Member (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: assignmentData.notes,\n                                        onChange: (e)=>setAssignmentData({\n                                                ...assignmentData,\n                                                notes: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                        rows: 3,\n                                        placeholder: \"Add any special instructions or notes...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowAssignModal(false);\n                                            setSelectedJob(null);\n                                            setAssignmentData({\n                                                staffId: \"\",\n                                                notes: \"\"\n                                            });\n                                        },\n                                        className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: submitAssignment,\n                                        disabled: !assignmentData.staffId,\n                                        className: \"bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Assign Job\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 574,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n_s(SupervisorDashboard, \"ZjmaA0bAJNPdOUQmPTNhmWo1FkQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SupervisorDashboard;\nvar _c;\n$RefreshReg$(_c, \"SupervisorDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/supervisor/page.tsx\n"));

/***/ })

});