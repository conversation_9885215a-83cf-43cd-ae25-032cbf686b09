/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/supervisor/page";
exports.ids = ["app/supervisor/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsupervisor%2Fpage&page=%2Fsupervisor%2Fpage&appPaths=%2Fsupervisor%2Fpage&pagePath=private-next-app-dir%2Fsupervisor%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsupervisor%2Fpage&page=%2Fsupervisor%2Fpage&appPaths=%2Fsupervisor%2Fpage&pagePath=private-next-app-dir%2Fsupervisor%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'supervisor',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/supervisor/page.tsx */ \"(rsc)/./src/app/supervisor/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/supervisor/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/supervisor/page\",\n        pathname: \"/supervisor\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsupervisor%2Fpage&page=%2Fsupervisor%2Fpage&appPaths=%2Fsupervisor%2Fpage&pagePath=private-next-app-dir%2Fsupervisor%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Csupervisor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Csupervisor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/supervisor/page.tsx */ \"(ssr)/./src/app/supervisor/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWUlNUMlNUNEb2N1bWVudHMlNUMlNUNQcm9qZWN0cyU1QyU1Q3N0YWZmaW5nYXBwJTVDJTVDc3RhZmZpbmdhcHAtd2ViJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDc3VwZXJ2aXNvciU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBdUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvPzg3MDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxIb21lXFxcXERvY3VtZW50c1xcXFxQcm9qZWN0c1xcXFxzdGFmZmluZ2FwcFxcXFxzdGFmZmluZ2FwcC13ZWJcXFxcc3JjXFxcXGFwcFxcXFxzdXBlcnZpc29yXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHome%5C%5CDocuments%5C%5CProjects%5C%5Cstaffingapp%5C%5Cstaffingapp-web%5C%5Csrc%5C%5Capp%5C%5Csupervisor%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/supervisor/page.tsx":
/*!*************************************!*\
  !*** ./src/app/supervisor/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupervisorDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(ssr)/./src/utils/statusDisplay.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Utility function to format enum names with proper spacing\nconst formatEnumName = (enumValue)=>{\n    return enumValue.replace(/([A-Z])/g, \" $1\").trim();\n};\nfunction SupervisorDashboard() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [staffMembers, setStaffMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pending\");\n    const [showAssignModal, setShowAssignModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedJob, setSelectedJob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assignmentData, setAssignmentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        staffId: \"\",\n        notes: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = ()=>{\n            const token = sessionStorage.getItem(\"token\");\n            const userStr = sessionStorage.getItem(\"user\");\n            if (!token || !userStr) {\n                router.push(\"/login\");\n                return;\n            }\n            const userData = JSON.parse(userStr);\n            if (userData.role !== \"Supervisor\") {\n                router.push(\"/dashboard\");\n                return;\n            }\n            setUser(userData);\n            fetchJobs();\n            fetchStaffMembers();\n        };\n        checkAuth();\n    }, [\n        router\n    ]);\n    const fetchJobs = async ()=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/jobs/all\", {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const jobsData = await response.json();\n                setJobs(jobsData);\n            } else {\n                console.error(\"Failed to fetch jobs:\", response.status, response.statusText);\n            }\n        } catch (error) {\n            console.error(\"Error fetching jobs:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const fetchStaffMembers = async ()=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(\"http://localhost:5000/api/auth/users/staff\", {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                const staffData = await response.json();\n                setStaffMembers(staffData);\n            } else {\n                console.error(\"Failed to fetch staff members:\", response.status, response.statusText);\n            }\n        } catch (error) {\n            console.error(\"Error fetching staff members:\", error);\n        }\n    };\n    const handleLogout = ()=>{\n        sessionStorage.clear();\n        document.cookie = \"token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;\";\n        window.location.href = \"/login\";\n    };\n    const handleAssignJob = (job)=>{\n        setSelectedJob(job);\n        setShowAssignModal(true);\n        setAssignmentData({\n            staffId: \"\",\n            notes: \"\"\n        });\n    };\n    const handleRejectJob = async (jobId)=>{\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(`http://localhost:5000/api/jobs/${jobId}/reject`, {\n                method: \"PUT\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (response.ok) {\n                fetchJobs(); // Refresh the jobs list\n            }\n        } catch (error) {\n            console.error(\"Error rejecting job:\", error);\n        }\n    };\n    const submitAssignment = async ()=>{\n        if (!selectedJob || !assignmentData.staffId) return;\n        try {\n            const token = sessionStorage.getItem(\"token\");\n            const response = await fetch(`http://localhost:5000/api/jobs/${selectedJob.id}/assign`, {\n                method: \"PUT\",\n                headers: {\n                    \"Authorization\": `Bearer ${token}`,\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    staffId: parseInt(assignmentData.staffId),\n                    notes: assignmentData.notes\n                })\n            });\n            if (response.ok) {\n                setShowAssignModal(false);\n                setSelectedJob(null);\n                setAssignmentData({\n                    staffId: \"\",\n                    notes: \"\"\n                });\n                fetchJobs(); // Refresh the jobs list\n            }\n        } catch (error) {\n            console.error(\"Error assigning job:\", error);\n        }\n    };\n    const getJobsByStatus = (status)=>{\n        return jobs.filter((job)=>job.status === status);\n    };\n    const getStatusColor = (status)=>{\n        return (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getStatusTailwindClasses)(status);\n    };\n    const getStatusDisplay = (status)=>{\n        return (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(status, \"Supervisor\");\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Loading supervisor dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this);\n    }\n    const pendingJobs = getJobsByStatus(\"New\");\n    const underReviewJobs = getJobsByStatus(\"Returned\");\n    const assignedJobs = getJobsByStatus(\"Assigned\");\n    const inProgressJobs = getJobsByStatus(\"InProgress\");\n    const completedJobs = getJobsByStatus(\"AwaitingReview\"); // Jobs completed by staff, awaiting supervisor review\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Staff Hall - Supervisor Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-700\",\n                                        children: [\n                                            \"Welcome, \",\n                                            user?.firstName,\n                                            \" \",\n                                            user?.lastName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Sign Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"pt-24 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-5 gap-6 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: pendingJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"New\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    pendingJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: assignedJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"Assigned\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    assignedJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-emerald-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: inProgressJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"InProgress\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    inProgressJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: completedJobs.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"AwaitingReview\", \"Supervisor\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    completedJobs.length,\n                                                                    \" jobs\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white overflow-hidden shadow rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-bold\",\n                                                            children: staffMembers.length\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-5 w-0 flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dl\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dt\", {\n                                                                className: \"text-sm font-medium text-gray-500 truncate\",\n                                                                children: \"Staff Members\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"dd\", {\n                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                children: [\n                                                                    staffMembers.length,\n                                                                    \" active\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"-mb-px flex space-x-8 px-6\",\n                                        \"aria-label\": \"Tabs\",\n                                        children: [\n                                            {\n                                                id: \"pending\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"New\", \"Supervisor\"),\n                                                count: pendingJobs.length\n                                            },\n                                            {\n                                                id: \"assigned\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"Assigned\", \"Supervisor\"),\n                                                count: assignedJobs.length\n                                            },\n                                            {\n                                                id: \"progress\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"InProgress\", \"Supervisor\"),\n                                                count: inProgressJobs.length\n                                            },\n                                            {\n                                                id: \"completed\",\n                                                name: (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"AwaitingReview\", \"Supervisor\"),\n                                                count: completedJobs.length\n                                            },\n                                            {\n                                                id: \"staff\",\n                                                name: \"Staff Management\",\n                                                count: staffMembers.length\n                                            }\n                                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: `${activeTab === tab.id ? \"border-emerald-500 text-emerald-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`,\n                                                children: [\n                                                    tab.name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs\",\n                                                        children: tab.count\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        activeTab === \"pending\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: [\n                                                        (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_3__.getRoleBasedStatusDisplay)(\"New\", \"Supervisor\"),\n                                                        \" Jobs\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                pendingJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No pending jobs to review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: pendingJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 366,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 367,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Submitted: \",\n                                                                                            formatDate(job.createdAt)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 368,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(`/jobs/${job.id}`),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAssignJob(job),\n                                                                                className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"Assign\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 378,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleRejectJob(job.id),\n                                                                                className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"Reject\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 384,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"assigned\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Assigned Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 19\n                                                }, this),\n                                                assignedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No assigned jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: assignedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-purple-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 410,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 411,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 413,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 414,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Assigned: \",\n                                                                                            job.assignedAt ? formatDate(job.assignedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 415,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Assigned to: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 420,\n                                                                                            columnNumber: 50\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 419,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 418,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            job.supervisorNotes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 p-2 bg-blue-50 rounded\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-blue-800\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: \"Notes:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 427,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        job.supervisorNotes\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 426,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 425,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 409,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(`/jobs/${job.id}`),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(job.status)}`,\n                                                                                children: getStatusDisplay(job.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 439,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"progress\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Jobs In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 19\n                                                }, this),\n                                                inProgressJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No jobs in progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: inProgressJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-emerald-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 465,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 466,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Started: \",\n                                                                                            job.assignedAt ? formatDate(job.assignedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 467,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Being worked on by: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 472,\n                                                                                            columnNumber: 57\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>router.push(`/jobs/${job.id}`),\n                                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm\",\n                                                                                children: \"View Details\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: `inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(job.status)}`,\n                                                                                children: getStatusDisplay(job.status)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 484,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Jobs Awaiting Supervisor Review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this),\n                                                completedJobs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No completed jobs awaiting review\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: completedJobs.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4 bg-green-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"text-lg font-medium text-gray-900\",\n                                                                                children: job.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 507,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 mt-1\",\n                                                                                children: job.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 508,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 flex items-center space-x-4 text-sm text-gray-500\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Type: \",\n                                                                                            formatEnumName(job.jobType)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 510,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Category: \",\n                                                                                            formatEnumName(job.category)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 511,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            \"Completed: \",\n                                                                                            job.completedAt ? formatDate(job.completedAt) : \"N/A\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                        lineNumber: 512,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 509,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            job.assignedTo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm text-gray-600\",\n                                                                                    children: [\n                                                                                        \"Completed by: \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: [\n                                                                                                job.assignedTo.firstName,\n                                                                                                \" \",\n                                                                                                job.assignedTo.lastName\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 517,\n                                                                                            columnNumber: 51\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 516,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 515,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            job.outputDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mt-2 p-3 bg-blue-50 rounded-md\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-blue-800\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: \"Output Details:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                            lineNumber: 524,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \" \",\n                                                                                        job.outputDetails\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                    lineNumber: 523,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                                lineNumber: 522,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 506,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4 flex items-center space-x-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>router.push(`/jobs/${job.id}`),\n                                                                            className: \"bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-2 rounded text-sm font-medium\",\n                                                                            children: \"Review & Deliver\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                            lineNumber: 530,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 529,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, job.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 497,\n                                            columnNumber: 17\n                                        }, this),\n                                        activeTab === \"staff\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-4\",\n                                                    children: \"Staff Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this),\n                                                staffMembers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500 text-center py-8\",\n                                                    children: \"No staff members found\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                    children: staffMembers.map((staff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border border-gray-200 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: [\n                                                                        staff.firstName,\n                                                                        \" \",\n                                                                        staff.lastName\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 text-sm\",\n                                                                    children: staff.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-emerald-100 text-emerald-800\",\n                                                                        children: formatEnumName(staff.role)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, staff.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                            lineNumber: 546,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this),\n            showAssignModal && selectedJob && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    \"Assign Job: \",\n                                    selectedJob.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Assign to Staff Member\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: assignmentData.staffId,\n                                        onChange: (e)=>setAssignmentData({\n                                                ...assignmentData,\n                                                staffId: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                        required: true,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select staff member...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 19\n                                            }, this),\n                                            staffMembers.map((staff)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: staff.id,\n                                                    children: [\n                                                        staff.firstName,\n                                                        \" \",\n                                                        staff.lastName,\n                                                        \" (\",\n                                                        staff.email,\n                                                        \")\"\n                                                    ]\n                                                }, staff.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 21\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 581,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Notes for Staff Member (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: assignmentData.notes,\n                                        onChange: (e)=>setAssignmentData({\n                                                ...assignmentData,\n                                                notes: e.target.value\n                                            }),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500\",\n                                        rows: 3,\n                                        placeholder: \"Add any special instructions or notes...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowAssignModal(false);\n                                            setSelectedJob(null);\n                                            setAssignmentData({\n                                                staffId: \"\",\n                                                notes: \"\"\n                                            });\n                                        },\n                                        className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: submitAssignment,\n                                        disabled: !assignmentData.staffId,\n                                        className: \"bg-emerald-600 hover:bg-emerald-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                        children: \"Assign Job\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                                lineNumber: 613,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n                lineNumber: 574,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\supervisor\\\\page.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/supervisor/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/statusDisplay.ts":
/*!************************************!*\
  !*** ./src/utils/statusDisplay.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRoleBasedStatusDisplay: () => (/* binding */ getRoleBasedStatusDisplay),\n/* harmony export */   getStatusColors: () => (/* binding */ getStatusColors),\n/* harmony export */   getStatusTailwindClasses: () => (/* binding */ getStatusTailwindClasses)\n/* harmony export */ });\n/**\n * Maps job statuses to role-specific display names\n */ const getRoleBasedStatusDisplay = (status, userRole)=>{\n    // Enhanced debug logging to see what we're receiving\n    console.log(\"\\uD83D\\uDD0D Status Display Debug:\", {\n        originalStatus: status,\n        userRole,\n        statusType: typeof status,\n        userRoleType: typeof userRole\n    });\n    // Check for null/undefined status\n    if (!status) {\n        console.warn(\"⚠️ Received null or undefined status\");\n        return \"Unknown\";\n    }\n    // Normalize status to handle both backend enum names and frontend display names\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\").toLowerCase();\n    console.log(\"\\uD83D\\uDCDD Normalized status:\", normalizedStatus, \"Original:\", status);\n    let result = status; // Default fallback\n    // Log which case we're entering\n    console.log(`🔄 Processing for role: ${userRole}`);\n    switch(userRole){\n        case \"user\":\n            console.log(`👤 User role case, normalized status: ${normalizedStatus}`);\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"Submitted\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"released\":\n                    result = \"Completed\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    console.warn(`⚠️ Unrecognized status for User role: ${normalizedStatus} (original: ${status})`);\n                    result = status;\n            }\n            break;\n        case \"supervisor\":\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting My Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"staff\":\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned to Me\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Supervisor Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        case \"admin\":\n            // Admin sees the system status names\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n        default:\n            // Default to system status names\n            switch(normalizedStatus.toLowerCase()){\n                case \"new\":\n                    result = \"New\";\n                    break;\n                case \"returned\":\n                    result = \"Returned\";\n                    break;\n                case \"assigned\":\n                    result = \"Assigned\";\n                    break;\n                case \"inprogress\":\n                    result = \"In Progress\";\n                    break;\n                case \"awaitingreview\":\n                    result = \"Awaiting Review\";\n                    break;\n                case \"released\":\n                    result = \"Released\";\n                    break;\n                case \"closed\":\n                    result = \"Closed\";\n                    break;\n                default:\n                    result = status;\n            }\n            break;\n    }\n    console.log(\"✅ Final result:\", {\n        originalStatus: status,\n        userRole,\n        normalizedStatus,\n        result\n    });\n    return result;\n};\n/**\n * Gets the color scheme for a job status badge\n */ const getStatusColors = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusColors = {\n        \"New\": {\n            bg: \"#ecfdf5\",\n            text: \"#065f46\",\n            border: \"#d1fae5\"\n        },\n        \"Returned\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Assigned\": {\n            bg: \"#eff6ff\",\n            text: \"#1e40af\",\n            border: \"#bfdbfe\"\n        },\n        \"InProgress\": {\n            bg: \"#f0fdf4\",\n            text: \"#14532d\",\n            border: \"#bbf7d0\"\n        },\n        \"AwaitingReview\": {\n            bg: \"#fef3c7\",\n            text: \"#92400e\",\n            border: \"#fde68a\"\n        },\n        \"Released\": {\n            bg: \"#e0e7ff\",\n            text: \"#3730a3\",\n            border: \"#c7d2fe\"\n        },\n        \"Closed\": {\n            bg: \"#dcfce7\",\n            text: \"#166534\",\n            border: \"#bbf7d0\"\n        }\n    };\n    return statusColors[normalizedStatus] || {\n        bg: \"#f3f4f6\",\n        text: \"#4b5563\",\n        border: \"#d1d5db\"\n    };\n};\n/**\n * Gets Tailwind CSS classes for status badges\n */ const getStatusTailwindClasses = (status)=>{\n    const normalizedStatus = status.replace(/[-\\s]/g, \"\");\n    const statusClasses = {\n        \"New\": \"bg-emerald-100 text-emerald-800\",\n        \"Returned\": \"bg-yellow-100 text-yellow-800\",\n        \"Assigned\": \"bg-blue-100 text-blue-800\",\n        \"InProgress\": \"bg-emerald-100 text-emerald-800\",\n        \"AwaitingReview\": \"bg-yellow-100 text-yellow-800\",\n        \"Released\": \"bg-purple-100 text-purple-800\",\n        \"Closed\": \"bg-green-100 text-green-800\"\n    };\n    return statusClasses[normalizedStatus] || \"bg-gray-100 text-gray-800\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/statusDisplay.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"927152f9cf66\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc3RhZmZpbmdhcHAtd2ViLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz83NzkzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTI3MTUyZjljZjY2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Staff Hall\",\n    description: \"A modern staffing solution\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFHTUE7QUFIZ0I7QUFLZixNQUFNQyxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdULCtKQUFlO3NCQUFHSzs7Ozs7Ozs7Ozs7QUFHekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhID0ge1xuICB0aXRsZTogJ1N0YWZmIEhhbGwnLFxuICBkZXNjcmlwdGlvbjogJ0EgbW9kZXJuIHN0YWZmaW5nIHNvbHV0aW9uJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/supervisor/page.tsx":
/*!*************************************!*\
  !*** ./src/app/supervisor/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\Projects\staffingapp\staffingapp-web\src\app\supervisor\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zdGFmZmluZ2FwcC13ZWIvLi9zcmMvYXBwL2Zhdmljb24uaWNvP2VhNzAiXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsupervisor%2Fpage&page=%2Fsupervisor%2Fpage&appPaths=%2Fsupervisor%2Fpage&pagePath=private-next-app-dir%2Fsupervisor%2Fpage.tsx&appDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHome%5CDocuments%5CProjects%5Cstaffingapp%5Cstaffingapp-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();