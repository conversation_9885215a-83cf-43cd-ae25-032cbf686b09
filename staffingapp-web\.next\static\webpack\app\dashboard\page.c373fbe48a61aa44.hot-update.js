"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/JobList.tsx":
/*!************************************!*\
  !*** ./src/components/JobList.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JobList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_job__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/job */ \"(app-pages-browser)/./src/lib/job.ts\");\n/* harmony import */ var _utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusDisplay */ \"(app-pages-browser)/./src/utils/statusDisplay.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Utility function to format enum names with proper spacing\nconst formatEnumName = (enumValue)=>{\n    return enumValue.replace(/([A-Z])/g, \" $1\").trim();\n};\nfunction JobList(param) {\n    let { onError, hoursStatistics } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [jobs, setJobs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!mounted) return;\n        const fetchJobs = async ()=>{\n            try {\n                console.log(\"JobList: Starting to fetch jobs...\");\n                setIsLoading(true);\n                setError(null);\n                // Check if we're in the browser before making API calls\n                if (false) {}\n                // Add delay to ensure authentication is ready\n                console.log(\"JobList: Waiting for authentication to be ready...\");\n                await new Promise((resolve)=>setTimeout(resolve, 200));\n                // Debug: Check authentication state\n                const token = sessionStorage.getItem(\"token\");\n                const user = sessionStorage.getItem(\"user\");\n                console.log(\"JobList: Auth check - Token exists:\", !!token, \"User exists:\", !!user);\n                console.log(\"JobList: SessionStorage length:\", sessionStorage.length);\n                if (token) {\n                    console.log(\"JobList: Token preview:\", token.substring(0, 20) + \"...\");\n                    console.log(\"JobList: Token length:\", token.length);\n                } else {\n                    console.log(\"JobList: No token found in sessionStorage\");\n                    console.log(\"JobList: All sessionStorage keys:\", Object.keys(sessionStorage));\n                    // Try to get token again after a short delay\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                    const retryToken = sessionStorage.getItem(\"token\");\n                    console.log(\"JobList: Retry token check:\", !!retryToken);\n                    if (!retryToken) {\n                        console.log(\"JobList: Still no token after retry, user might not be logged in\");\n                        setError(\"Please log in to view jobs\");\n                        setIsLoading(false);\n                        return;\n                    }\n                }\n                console.log(\"JobList: Calling getJobs()...\");\n                const jobsData = await (0,_lib_job__WEBPACK_IMPORTED_MODULE_3__.getJobs)();\n                console.log(\"JobList: Jobs fetched successfully:\", jobsData);\n                console.log(\"JobList: Number of jobs:\", jobsData.length);\n                setJobs(jobsData);\n            } catch (err) {\n                console.error(\"JobList: Error fetching jobs:\", err);\n                console.error(\"JobList: Error details:\", {\n                    message: err instanceof Error ? err.message : \"Unknown error\",\n                    stack: err instanceof Error ? err.stack : undefined\n                });\n                const errorMessage = err instanceof Error ? err.message : \"Failed to fetch jobs\";\n                setError(errorMessage);\n                onError === null || onError === void 0 ? void 0 : onError(errorMessage);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchJobs();\n    }, [\n        mounted,\n        onError\n    ]);\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        fontSize: \"18px\",\n                        fontWeight: \"500\",\n                        margin: \"0 0 16px 0\"\n                    },\n                    children: \"Work History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    style: {\n                        color: \"#6b7280\",\n                        margin: \"0\"\n                    },\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    }\n    const getStatusBadge = (status)=>{\n        const user = JSON.parse(sessionStorage.getItem(\"user\") || \"{}\");\n        const userRole = user.role || \"User\";\n        // Debug logging\n        console.log(\"\\uD83C\\uDFAF JobList getStatusBadge:\", {\n            status,\n            userRole,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role\n            },\n            sessionStorageUser: sessionStorage.getItem(\"user\")\n        });\n        const displayText = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getRoleBasedStatusDisplay)(status, userRole);\n        const colors = (0,_utils_statusDisplay__WEBPACK_IMPORTED_MODULE_4__.getStatusColors)(status);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            style: {\n                display: \"inline-flex\",\n                alignItems: \"center\",\n                padding: \"4px 12px\",\n                borderRadius: \"9999px\",\n                fontSize: \"12px\",\n                fontWeight: \"500\",\n                backgroundColor: colors.bg,\n                color: colors.text,\n                border: \"1px solid \".concat(colors.border)\n            },\n            children: displayText\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    alignItems: \"center\",\n                    height: \"128px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: \"#6b7280\"\n                    },\n                    children: \"Loading jobs...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\",\n                padding: \"24px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    borderRadius: \"6px\",\n                    backgroundColor: \"#fef2f2\",\n                    padding: \"16px\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"flex\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                flexShrink: 0\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                style: {\n                                    height: \"20px\",\n                                    width: \"20px\",\n                                    color: \"#f87171\"\n                                },\n                                viewBox: \"0 0 20 20\",\n                                fill: \"currentColor\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    fillRule: \"evenodd\",\n                                    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                                    clipRule: \"evenodd\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginLeft: \"12px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        color: \"#991b1b\",\n                                        margin: \"0 0 8px 0\"\n                                    },\n                                    children: \"Error Loading Jobs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"14px\",\n                                        color: \"#7f1d1d\",\n                                        margin: \"0\"\n                                    },\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, this);\n    }\n    if (jobs.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                backgroundColor: \"white\",\n                boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n                borderRadius: \"8px\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    padding: \"24px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            fontSize: \"18px\",\n                            fontWeight: \"500\",\n                            color: \"#111827\",\n                            margin: \"0 0 24px 0\"\n                        },\n                        children: \"Work History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"16px\",\n                            marginBottom: \"24px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"Total Jobs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M5 13l4 4L19 7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: \"#ecfdf5\",\n                                    padding: \"16px\",\n                                    borderRadius: \"12px\",\n                                    border: \"1px solid #d1fae5\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: \"40px\",\n                                                height: \"40px\",\n                                                backgroundColor: \"#059669\",\n                                                borderRadius: \"50%\",\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"center\",\n                                                marginRight: \"12px\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                style: {\n                                                    height: \"20px\",\n                                                    width: \"20px\",\n                                                    color: \"white\"\n                                                },\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: \"2\",\n                                                    d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"14px\",\n                                                        fontWeight: \"500\",\n                                                        color: \"#065f46\"\n                                                    },\n                                                    children: \"New\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"24px\",\n                                                        fontWeight: \"bold\",\n                                                        color: \"#064e3b\"\n                                                    },\n                                                    children: \"0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            textAlign: \"center\",\n                            padding: \"32px 0\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                style: {\n                                    margin: \"0 auto\",\n                                    height: \"48px\",\n                                    width: \"48px\",\n                                    color: \"#9ca3af\"\n                                },\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    marginTop: \"8px\",\n                                    fontSize: \"14px\",\n                                    fontWeight: \"500\",\n                                    color: \"#111827\",\n                                    margin: \"8px 0\"\n                                },\n                                children: \"No jobs found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    marginTop: \"4px\",\n                                    fontSize: \"14px\",\n                                    color: \"#6b7280\",\n                                    margin: \"4px 0 24px 0\"\n                                },\n                                children: hoursStatistics && hoursStatistics.hoursAvailable <= 0 ? \"Purchase hours to start submitting jobs.\" : \"Get started by submitting your first job.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this),\n                            hoursStatistics && hoursStatistics.hoursAvailable > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/submit-job\",\n                                    style: {\n                                        display: \"inline-flex\",\n                                        alignItems: \"center\",\n                                        padding: \"8px 16px\",\n                                        border: \"none\",\n                                        boxShadow: \"0 1px 2px 0 rgba(0, 0, 0, 0.05)\",\n                                        fontSize: \"14px\",\n                                        fontWeight: \"500\",\n                                        borderRadius: \"6px\",\n                                        color: \"white\",\n                                        backgroundColor: \"#059669\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: \"Submit Job\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 336,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    // Calculate summary statistics for all statuses\n    const totalJobs = jobs.length;\n    // Normalize job status for consistent comparison\n    const normalizeStatus = (status)=>status ? status.replace(/[-\\s]/g, \"\").toLowerCase() : \"\";\n    // Count jobs by normalized status\n    const newJobs = jobs.filter((job)=>normalizeStatus(job.status) === \"new\").length;\n    const inProgressJobs = jobs.filter((job)=>normalizeStatus(job.status) === \"inprogress\").length;\n    const awaitingReviewJobs = jobs.filter((job)=>normalizeStatus(job.status) === \"awaitingreview\").length;\n    const completedJobs = jobs.filter((job)=>normalizeStatus(job.status) === \"closed\").length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            backgroundColor: \"white\",\n            boxShadow: \"0 1px 3px 0 rgba(0, 0, 0, 0.1)\",\n            borderRadius: \"8px\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: \"24px\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    style: {\n                        fontSize: \"18px\",\n                        fontWeight: \"500\",\n                        color: \"#111827\",\n                        margin: \"0 0 24px 0\"\n                    },\n                    children: \"Work History\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: \"grid\",\n                        gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                        gap: \"16px\",\n                        marginBottom: \"24px\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"Total Jobs\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: totalJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M5 13l4 4L19 7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: completedJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"In Progress\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: inProgressJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundColor: \"#ecfdf5\",\n                                padding: \"16px\",\n                                borderRadius: \"12px\",\n                                border: \"1px solid #d1fae5\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            width: \"40px\",\n                                            height: \"40px\",\n                                            backgroundColor: \"#059669\",\n                                            borderRadius: \"50%\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"center\",\n                                            marginRight: \"12px\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            style: {\n                                                height: \"20px\",\n                                                width: \"20px\",\n                                                color: \"white\"\n                                            },\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: \"2\",\n                                                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"14px\",\n                                                    fontWeight: \"500\",\n                                                    color: \"#065f46\"\n                                                },\n                                                children: \"New\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: \"24px\",\n                                                    fontWeight: \"bold\",\n                                                    color: \"#064e3b\"\n                                                },\n                                                children: newJobs\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        overflow: \"hidden\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        style: {\n                            minWidth: \"100%\",\n                            borderCollapse: \"collapse\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                style: {\n                                    backgroundColor: \"#f9fafb\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Job\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            style: {\n                                                padding: \"12px 24px\",\n                                                textAlign: \"left\",\n                                                fontSize: \"12px\",\n                                                fontWeight: \"500\",\n                                                color: \"#6b7280\",\n                                                textTransform: \"uppercase\",\n                                                letterSpacing: \"0.05em\"\n                                            },\n                                            children: \"Created\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                style: {\n                                    backgroundColor: \"white\"\n                                },\n                                children: jobs.map((job, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        style: {\n                                            borderTop: index > 0 ? \"1px solid #e5e7eb\" : \"none\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"14px\",\n                                                                fontWeight: \"500\",\n                                                                color: \"#059669\",\n                                                                cursor: \"pointer\",\n                                                                textDecoration: \"underline\"\n                                                            },\n                                                            onClick: ()=>router.push(\"/jobs/\".concat(job.id)),\n                                                            onMouseEnter: (e)=>{\n                                                                e.currentTarget.style.color = \"#047857\";\n                                                            },\n                                                            onMouseLeave: (e)=>{\n                                                                e.currentTarget.style.color = \"#059669\";\n                                                            },\n                                                            children: job.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                            lineNumber: 605,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: \"14px\",\n                                                                color: \"#6b7280\",\n                                                                overflow: \"hidden\",\n                                                                textOverflow: \"ellipsis\",\n                                                                maxWidth: \"300px\"\n                                                            },\n                                                            children: job.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: \"#111827\"\n                                                        },\n                                                        children: formatEnumName(job.jobType)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                        lineNumber: 636,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: \"14px\",\n                                                            color: \"#6b7280\"\n                                                        },\n                                                        children: formatEnumName(job.category)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\"\n                                                },\n                                                children: getStatusBadge(job.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                style: {\n                                                    padding: \"16px 24px\",\n                                                    whiteSpace: \"nowrap\",\n                                                    fontSize: \"14px\",\n                                                    color: \"#6b7280\"\n                                                },\n                                                children: formatDate(job.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, job.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n                    lineNumber: 545,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n            lineNumber: 419,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\staffingapp\\\\staffingapp-web\\\\src\\\\components\\\\JobList.tsx\",\n        lineNumber: 414,\n        columnNumber: 5\n    }, this);\n}\n_s(JobList, \"CHYTzYO3MIcSRksHsX403h10Jv0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = JobList;\nvar _c;\n$RefreshReg$(_c, \"JobList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JobList.tsx\n"));

/***/ })

});